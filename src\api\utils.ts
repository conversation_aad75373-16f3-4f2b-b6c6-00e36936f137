export const baseUrlApi = (url: string) => {
  // 开发环境：Vite 代理会移除 /api，所以需要 /api/api/v1/...
  // 生产环境：nginx 直接转发，所以需要 /api/v1/...
  return process.env.NODE_ENV === "development" ? `/api/${url}` : `/${url}`;
};

export const simpleUploadUrl = () => {
  return process.env.NODE_ENV === "development"
    ? "/api/api/v1/fs/upload_chunk"
    : "/api/v1/fs/upload_chunk";
};

export const downLoadAudioFileUrl =
  process.env.NODE_ENV === "development"
    ? "/api/api/v1/voice2text/result/"
    : "/api/v1/voice2text/result/";

export const downLoadPdfFileUrl =
  process.env.NODE_ENV === "development"
    ? "/api/api/v1/pdf2word/result/"
    : "/api/v1/pdf2word/result/";

export const downLoadOfdPdfFileUrl =
  process.env.NODE_ENV === "development"
    ? "/api/api/v1/ofd2pdf/result/"
    : "/api/v1/ofd2pdf/result/";

export const downLoadOfdWordFileUrl =
  process.env.NODE_ENV === "development"
    ? "/api/api/v1/ofd2word/result/"
    : "/api/v1/ofd2word/result/";
